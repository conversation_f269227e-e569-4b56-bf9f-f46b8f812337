using Application.Abstractions.Data;
using Microsoft.EntityFrameworkCore;
using Web.Api.Extensions;

namespace Web.Api.Endpoints.History;

internal sealed class GetHistory : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("history/{tableName}", GetHistoryAsync)
            .WithTags("History")
            .WithName("GetHistory")
            .Produces<List<HistoryResponse>>()
            .RequireAuthorization();
    }

    private static async Task<IResult> GetHistoryAsync(
        string tableName,
        IApplicationDbContext context,
        CancellationToken cancellationToken)
    {
        List<HistoryResponse> history = await context.Histories
            .Where(h => h.TableName == tableName)
            .OrderByDescending(h => h.CreatedAt)
            .Select(h => new HistoryResponse(
                h.Id,
                h.TableName,
                h.PropertyName,
                h.PropertyType,
                h.PreviousValue,
                h.<PERSON>al<PERSON>,
                h.<PERSON>t))
            .ToListAsync(cancellationToken);

        return Results.Ok(history);
    }
}

public sealed record HistoryResponse(
    int Id,
    string TableName,
    string PropertyName,
    string PropertyType,
    string PreviousValue,
    string CurrentValue,
    DateTime CreatedAt);
